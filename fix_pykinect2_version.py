#!/usr/bin/env python3
"""
Fix pykinect2 version check issue
This removes the problematic version check that prevents pykinect2 from working.
"""

import os
import sys


def find_pykinect_file():
    """Find the PyKinectV2.py file."""
    try:
        import pykinect2
        package_dir = os.path.dirname(pykinect2.__file__)
        pykinect_file = os.path.join(package_dir, "PyKinectV2.py")
        return pykinect_file
    except ImportError:
        return None


def fix_version_check(file_path):
    """Remove the problematic version check."""
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return False
    
    try:
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find and comment out the problematic version check
        old_line = "from comtypes import _check_version; _check_version('')"
        new_line = "# from comtypes import _check_version; _check_version('')  # Disabled for compatibility"
        
        if old_line in content:
            content = content.replace(old_line, new_line)
            
            # Write back the fixed content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"Successfully disabled version check in {file_path}")
            return True
        else:
            print("Version check line not found or already fixed.")
            return False
            
    except Exception as e:
        print(f"Error fixing file: {e}")
        return False


def main():
    print("=== PyKinect2 Version Check Fix ===")
    
    # Find the PyKinectV2.py file
    pykinect_file = find_pykinect_file()
    if pykinect_file is None:
        print("Error: pykinect2 not found. Please install it first:")
        print("py -m pip install pykinect2")
        return 1
    
    print(f"Found PyKinectV2.py at: {pykinect_file}")
    
    # Apply the fix
    if fix_version_check(pykinect_file):
        print("\nFix applied successfully!")
        print("PyKinect2 should now work without version check errors.")
    else:
        print("\nFailed to apply fix.")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
