#!/usr/bin/env python3
"""
Fix pykinect2 time.clock() issue for Python 3.8+
time.clock() was removed in Python 3.8, replace with time.perf_counter()
"""

import os
import sys


def find_pykinect_runtime_file():
    """Find the PyKinectRuntime.py file."""
    try:
        import pykinect2
        package_dir = os.path.dirname(pykinect2.__file__)
        runtime_file = os.path.join(package_dir, "PyKinectRuntime.py")
        return runtime_file
    except ImportError:
        return None


def fix_time_clock(file_path):
    """Replace time.clock() with time.perf_counter()."""
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return False
    
    try:
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace all occurrences of time.clock() with time.perf_counter()
        replacements = [
            ("time.clock()", "time.perf_counter()"),
            ("start_clock = time.clock()", "start_clock = time.perf_counter()"),
            ("clock = time.clock()", "clock = time.perf_counter()"),
        ]
        
        fixed_any = False
        for old_text, new_text in replacements:
            if old_text in content:
                content = content.replace(old_text, new_text)
                print(f"Replaced: {old_text} -> {new_text}")
                fixed_any = True

        if fixed_any:
            # Write back the fixed content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            print(f"Successfully fixed time.clock() issues in {file_path}")
            return True
        else:
            print("No time.clock() issues found or already fixed.")
            return False
            
    except Exception as e:
        print(f"Error fixing file: {e}")
        return False


def main():
    print("=== PyKinect2 time.clock() Fix ===")
    
    # Find the PyKinectRuntime.py file
    runtime_file = find_pykinect_runtime_file()
    if runtime_file is None:
        print("Error: pykinect2 not found. Please install it first:")
        print("py -m pip install pykinect2")
        return 1
    
    print(f"Found PyKinectRuntime.py at: {runtime_file}")
    
    # Apply the fix
    if fix_time_clock(runtime_file):
        print("\nFix applied successfully!")
        print("PyKinect2 should now work with Python 3.8+.")
    else:
        print("\nFailed to apply fix.")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
