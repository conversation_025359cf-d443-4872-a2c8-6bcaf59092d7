#!/usr/bin/env python3
"""
Test real Kinect data and verify IR illuminator is working
This will show actual depth values and verify the sensor is getting real data.
"""

import sys
import time
import numpy as np
from pykinect2 import PyKinectRuntime, PyKinectV2


def test_real_kinect_data():
    """Test that we're getting real data from Kinect sensor."""
    print("=== Testing Real Kinect Data ===")
    
    # Initialize Kinect with depth and infrared
    print("Initializing Kinect with depth and infrared streams...")
    kinect = PyKinectRuntime.PyKinectRuntime(
        PyKinectV2.FrameSourceTypes_Depth | PyKinectV2.FrameSourceTypes_Infrared
    )
    
    try:
        print("✅ Kinect initialized!")
        print("🔴 IR illuminator should now be ON (red light visible on camera)")
        print("\nWaiting for frames...")
        
        depth_stats = []
        ir_stats = []
        
        # Collect data for 50 frames
        for frame_num in range(50):
            # Get depth frame
            if kinect.has_new_depth_frame():
                depth_frame = kinect.get_last_depth_frame()
                depth_array = depth_frame.astype(np.uint16)
                depth_array = depth_array.reshape((kinect.depth_frame_desc.Height, kinect.depth_frame_desc.Width))
                
                # Analyze depth data
                valid_depths = depth_array[(depth_array > 0) & (depth_array < 8000)]
                if len(valid_depths) > 0:
                    min_depth = np.min(valid_depths)
                    max_depth = np.max(valid_depths)
                    mean_depth = np.mean(valid_depths)
                    valid_pixels = len(valid_depths)
                    
                    depth_stats.append({
                        'frame': frame_num,
                        'min': min_depth,
                        'max': max_depth,
                        'mean': mean_depth,
                        'valid_pixels': valid_pixels
                    })
                    
                    if frame_num % 10 == 0:
                        print(f"Depth frame {frame_num}: {valid_pixels} valid pixels, "
                              f"range {min_depth}-{max_depth}mm, mean {mean_depth:.0f}mm")
            
            # Get infrared frame
            if kinect.has_new_infrared_frame():
                ir_frame = kinect.get_last_infrared_frame()
                ir_array = ir_frame.astype(np.uint16)
                ir_array = ir_array.reshape((kinect.infrared_frame_desc.Height, kinect.infrared_frame_desc.Width))
                
                # Analyze IR data
                min_ir = np.min(ir_array)
                max_ir = np.max(ir_array)
                mean_ir = np.mean(ir_array)
                
                ir_stats.append({
                    'frame': frame_num,
                    'min': min_ir,
                    'max': max_ir,
                    'mean': mean_ir
                })
                
                if frame_num % 10 == 0:
                    print(f"IR frame {frame_num}: range {min_ir}-{max_ir}, mean {mean_ir:.0f}")
            
            time.sleep(0.1)
        
        # Print summary
        print(f"\n--- Results Summary ---")
        print(f"Depth frames received: {len(depth_stats)}")
        print(f"IR frames received: {len(ir_stats)}")
        
        if depth_stats:
            avg_valid_pixels = np.mean([s['valid_pixels'] for s in depth_stats])
            avg_depth = np.mean([s['mean'] for s in depth_stats])
            print(f"Average valid pixels per frame: {avg_valid_pixels:.0f}")
            print(f"Average depth: {avg_depth:.0f}mm ({avg_depth/1000:.2f}m)")
            
        if ir_stats:
            avg_ir = np.mean([s['mean'] for s in ir_stats])
            print(f"Average IR intensity: {avg_ir:.0f}")
            
        if depth_stats or ir_stats:
            print("🎉 SUCCESS: Getting real data from Kinect!")
            print("🔴 IR illuminator is working (you should see red light)")
            return True
        else:
            print("❌ No real data received")
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        print("\nClosing Kinect...")
        kinect.close()


def main():
    success = test_real_kinect_data()
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
