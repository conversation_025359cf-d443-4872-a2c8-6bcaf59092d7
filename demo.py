#!/usr/bin/env python3
"""
Kinect10 Demo - Test script for terrain change detection

Usage:
    python demo.py

Controls:
    ESC - Exit
    
Requirements:
    - Kinect for Windows v2 connected
    - Kinect SDK 2.0 installed
    - All Python dependencies installed
"""

import sys
import traceback
from detector import Ki<PERSON>TerrainDetector


def main():
    print("=== Kinect10 Demo ===")
    print("Initializing Kinect terrain detector...")
    
    try:
        # Create detector with reasonable defaults
        detector = KinectTerrainDetector(
            baseline_frames=30,      # Number of frames to average for baseline
            min_blob_area_px=500,    # Minimum blob size in pixels
            diff_threshold_mm=60,    # Depth difference threshold in mm
            smooth_kernel=5,         # Smoothing kernel size
            display=True             # Show visualization
        )
        
        print("Detector created successfully!")
        print("\nInstructions:")
        print("1. Keep the scene static for baseline capture (~3 seconds)")
        print("2. After baseline is captured, place objects in the scene")
        print("3. Objects will be detected and highlighted in green")
        print("4. Press ESC to exit")
        print("\nStarting detection loop...")
        
        # Run the detector
        detections = detector.run()
        
        print(f"\nDetection completed. Last frame had {len(detections)} objects detected.")
        for i, det in enumerate(detections):
            print(f"  Object {i+1}: {det['distance_m']:.2f}m away, "
                  f"size {det['size_m'][0]*100:.1f}x{det['size_m'][1]*100:.1f}cm")
                  
    except ImportError as e:
        print(f"Import error: {e}")
        print("\nMake sure you have:")
        print("- Kinect SDK 2.0 installed")
        print("- pykinect2 package installed: py -m pip install pykinect2")
        print("- opencv-python package installed: py -m pip install opencv-python")
        print("- numpy package installed: py -m pip install numpy")
        return 1
        
    except Exception as e:
        print(f"Error: {e}")
        print("\nFull traceback:")
        traceback.print_exc()
        print("\nMake sure:")
        print("- Kinect v2 is connected and powered on")
        print("- Kinect SDK 2.0 is properly installed")
        print("- No other applications are using the Kinect")
        return 1
    
    print("Demo completed successfully!")
    return 0


if __name__ == "__main__":
    sys.exit(main())
