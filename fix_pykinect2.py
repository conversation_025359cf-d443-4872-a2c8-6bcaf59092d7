#!/usr/bin/env python3
"""
Fix for pykinect2 AssertionError on 64-bit Windows

This script fixes the known issue where pykinect2 fails with:
AssertionError: 80 (expected 72)

The fix changes the assertion in PyKinectV2.py from 72 to 80.
"""

import os
import sys


def find_pykinect_file():
    """Find the PyKinectV2.py file in the installed packages."""
    try:
        import pykinect2
        package_dir = os.path.dirname(pykinect2.__file__)
        pykinect_file = os.path.join(package_dir, "PyKinectV2.py")
        return pykinect_file
    except ImportError:
        return None


def fix_assertion_error(file_path):
    """Fix the assertion error by changing 72 to 80."""
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return False
    
    try:
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find and replace the problematic assertion
        old_assertion = "assert sizeof(tagSTATSTG) == 72, sizeof(tagSTATSTG)"
        new_assertion = "assert sizeof(tagSTATSTG) == 80, sizeof(tagSTATSTG)"
        
        if old_assertion in content:
            content = content.replace(old_assertion, new_assertion)
            
            # Write back the fixed content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"Successfully fixed assertion in {file_path}")
            print("Changed: sizeof(tagSTATSTG) == 72 -> sizeof(tagSTATSTG) == 80")
            return True
        else:
            print("Assertion not found or already fixed.")
            return False
            
    except Exception as e:
        print(f"Error fixing file: {e}")
        return False


def main():
    print("=== PyKinect2 Fix for 64-bit Windows ===")
    
    # Find the PyKinectV2.py file
    pykinect_file = find_pykinect_file()
    if pykinect_file is None:
        print("Error: pykinect2 not found. Please install it first:")
        print("py -m pip install pykinect2")
        return 1
    
    print(f"Found PyKinectV2.py at: {pykinect_file}")
    
    # Apply the fix
    if fix_assertion_error(pykinect_file):
        print("\nFix applied successfully!")
        print("You can now use pykinect2 without the AssertionError.")
    else:
        print("\nFailed to apply fix.")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
