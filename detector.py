from typing import List, Tu<PERSON>, Optional, Dict
import time

import numpy as np

try:
    # PyKinect2 is the official Python wrapper for Kinect SDK 2.0 community port
    from pykinect2 import PyKinectRuntime
    from pykinect2 import PyKinectV2
except Exception as e:
    PyKinectRuntime = None
    PyKinectV2 = None

try:
    import cv2
except Exception:
    cv2 = None


class Detection:
    def __init__(self, bbox: Tuple[int, int, int, int], size_m: <PERSON><PERSON>[float, float], distance_m: float, centroid_px: Tuple[int, int]):
        self.x, self.y, self.w, self.h = bbox
        self.width_m, self.height_m = size_m
        self.distance_m = distance_m
        self.cx, self.cy = centroid_px

    def to_dict(self) -> Dict:
        return {
            "bbox": (self.x, self.y, self.w, self.h),
            "size_m": (self.width_m, self.height_m),
            "distance_m": self.distance_m,
            "centroid_px": (self.cx, self.cy),
        }


class KinectTerrainDetector:
    """
    Detects new objects appearing relative to an initialization depth frame.

    Pipeline:
    - Grab initial depth frame as baseline (median of N frames)
    - For each frame:
       * Compute absolute difference vs baseline
       * Threshold and morph to create blobs
       * For each blob, estimate distance from depth map and size using Kinect intrinsics
       * Overlay results
    """

    def __init__(self, baseline_frames: int = 30, min_blob_area_px: int = 500, diff_threshold_mm: int = 60,
                 smooth_kernel: int = 5, display: bool = True):
        self.baseline_frames = baseline_frames
        self.min_blob_area_px = min_blob_area_px
        self.diff_threshold_mm = diff_threshold_mm
        self.smooth_kernel = smooth_kernel
        self.display = display

        self.kinect: Optional[PyKinectRuntime.PyKinectRuntime] = None
        self.baseline_depth: Optional[np.ndarray] = None
        self._intrinsics = None  # To be filled from Kinect API when available

        if PyKinectRuntime is None:
            raise ImportError("PyKinect2 not available. Please install 'pykinect2' and Kinect SDK 2.0.")
        if cv2 is None:
            raise ImportError("OpenCV not available. Please install 'opencv-python'.")

    def _start(self):
        # Start depth stream only (overlay uses depth-space visualization)
        self.kinect = PyKinectRuntime.PyKinectRuntime(PyKinectV2.FrameSourceTypes_Depth)

    def _stop(self):
        if self.kinect is not None:
            self.kinect.close()
            self.kinect = None

    def _capture_depth_frame(self) -> Optional[np.ndarray]:
        if self.kinect is None:
            return None
        if self.kinect.has_new_depth_frame():
            depth = self.kinect.get_last_depth_frame()
            depth = depth.astype(np.uint16)
            depth = depth.reshape((self.kinect.depth_frame_desc.Height, self.kinect.depth_frame_desc.Width))
            return depth
        return None

    def _capture_color_frame(self) -> Optional[np.ndarray]:
        # Not used in current demo (we overlay on depth). Kept for future color-space mapping.
        if self.kinect is None:
            return None
        return None

    def _compute_baseline(self):
        frames = []
        start = time.time()
        while len(frames) < self.baseline_frames and time.time() - start < 10:
            depth = self._capture_depth_frame()
            if depth is not None:
                frames.append(depth)
        if not frames:
            raise RuntimeError("Failed to capture baseline depth frames.")
        self.baseline_depth = np.median(np.stack(frames, axis=0), axis=0).astype(np.uint16)

    def _estimate_distance(self, depth_patch: np.ndarray) -> float:
        # Use median depth in mm, convert to meters
        valid = depth_patch[(depth_patch > 0) & (depth_patch < 4500)]
        if valid.size == 0:
            return float("nan")
        return float(np.median(valid)) / 1000.0

    def _estimate_size(self, bbox: Tuple[int, int, int, int], distance_m: float) -> Tuple[float, float]:
        # Approximate using Kinect v2 nominal intrinsics (focal lengths in pixels)
        # For accuracy, this should be read from sensor intrinsics
        fx = 366.1  # placeholder typical value
        fy = 366.1
        x, y, w, h = bbox
        width_m = (w / fx) * distance_m
        height_m = (h / fy) * distance_m
        return width_m, height_m

    def _find_blobs(self, diff_mm: np.ndarray) -> List[Tuple[int, int, int, int]]:
        # Threshold and morphology to get contours
        mask = (diff_mm > self.diff_threshold_mm).astype(np.uint8) * 255
        if self.smooth_kernel > 0:
            k = self.smooth_kernel | 1
            mask = cv2.medianBlur(mask, k)
            kernel = np.ones((3, 3), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)
            mask = cv2.dilate(mask, kernel, iterations=1)
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        bboxes = []
        for c in contours:
            x, y, w, h = cv2.boundingRect(c)
            if w * h >= self.min_blob_area_px:
                bboxes.append((x, y, w, h))
        return bboxes

    def _overlay(self, bgr: np.ndarray, detections: List[Detection]) -> np.ndarray:
        out = bgr.copy()
        for d in detections:
            x, y, w, h = d.x, d.y, d.w, d.h
            cv2.rectangle(out, (x, y), (x + w, y + h), (0, 255, 0), 2)
            label = f"{d.distance_m:.2f}m {d.width_m*100:.0f}x{d.height_m*100:.0f}cm"
            cv2.putText(out, label, (x, max(0, y - 5)), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            cv2.circle(out, (d.cx, d.cy), 3, (0, 0, 255), -1)
        return out

    def run(self, max_frames: Optional[int] = None) -> List[Dict]:
        """Run the detector loop. Returns last frame's detections as list of dicts."""
        self._start()
        try:
            self._compute_baseline()
            detections_dicts: List[Dict] = []
            frames_processed = 0
            while True:
                depth = self._capture_depth_frame()
                if depth is None:
                    cv2.waitKey(1)
                    continue
                diff = cv2.absdiff(depth, self.baseline_depth)
                bboxes = self._find_blobs(diff)
                detections: List[Detection] = []
                for (x, y, w, h) in bboxes:
                    patch = depth[y:y + h, x:x + w]
                    dist_m = self._estimate_distance(patch)
                    size_m = self._estimate_size((x, y, w, h), dist_m if not np.isnan(dist_m) else 1.0)
                    cx, cy = x + w // 2, y + h // 2
                    detections.append(Detection((x, y, w, h), size_m, dist_m, (cx, cy)))
                if self.display:
                    depth_bgr = self._depth_to_bgr(depth)
                    vis = self._overlay(depth_bgr, detections)
                    cv2.imshow("Kinect10 - detections", vis)
                detections_dicts = [d.to_dict() for d in detections]
                frames_processed += 1
                if self.display:
                    if (cv2.waitKey(1) & 0xFF) == 27:
                        break
                if max_frames is not None and frames_processed >= max_frames:
                    break
            return detections_dicts
        finally:
            self._stop()
            if self.display:
                try:
                    cv2.destroyAllWindows()
                except Exception:
                    pass


    def _depth_to_bgr(self, depth: np.ndarray) -> np.ndarray:
        # Convert depth in mm to a visualizable BGR image for overlay
        d = depth.astype(np.float32)
        invalid = (d <= 0) | (d > 4500)
        d[invalid] = np.nan
        vmax = 4500.0
        vmin = 500.0
        norm = (d - vmin) / (vmax - vmin)
        norm = np.clip(norm, 0, 1)
        norm_u8 = (norm * 255).astype(np.uint8)
        bgr = cv2.applyColorMap(norm_u8, cv2.COLORMAP_JET)
        bgr[np.isnan(d)] = (0, 0, 0)
        return bgr

